<template>
  <!-- 资源数量统计 个人 队伍-->
  <div class="plugin-view">
    <h3 class="plugin-title">{{ pluginTitle }}</h3>
    <div v-loading="true" v-if="loading" class="plugin-loading" />
    <el-empty v-else-if="!apiData" :image="noDataImg" :image-size="120" style="padding: 0;height: 100%;" description="暂无数据" />

    <!-- 检测项目数量统计和问题数量统计使用原有样式 -->
    <div v-else-if="['test_project_count', 'test_pass_rate'].includes(pluginApiType)" class="plugin-quantity-wrap">
      <div class="plugin-quantity-content">
        <div
          v-for="item of resourcesConfig.options"
          :key="item.label"
          class="quantity-item"
        >
          <div v-if="pluginApiType === 'test_project_count'" class="custom-icon">
            <i :class="item.icon" :style="changeFontSize(item)"/>
          </div>
          <div v-else-if="pluginApiType === 'test_pass_rate'" class="custom-icon">
            <i :class="item.icon" :style="changeFontSize(item)"/>
          </div>
          <div class="value">
            <template>
              {{ apiData[item] }}
              {{ apiData[item.countKey] }} {{ getUnit }}
              <span v-if="item.countKey === 'currentYear' && showTrend" :class="{'trend-up': isTrendUp, 'trend-down': !isTrendUp}" class="trend">
                <i v-if="apiData.trend === 1" class="trend-up el-icon-top"/>
                <i v-else-if="apiData.trend === 2" class="trend-down el-icon-bottom"/>
                {{ apiData.changeRate }}
              </span>
            </template>
          </div>
          <div class="label">{{ item.label }}</div>
        </div>
      </div>
    </div>

    <!-- 检测项目使用新样式 -->
    <div v-else-if="pluginApiType === 'vendor_test_project'" class="application-status-wrap">
      <div class="left-section">
        <div class="icon-container">
          <i class="cr-icon-csjcxm" style="font-size: 36px;color:var(--color-600)"/>
        </div>
        <div class="total-section">
          <div class="total-number">{{ apiData.total }}</div>
          <div class="total-label">总数量</div>
        </div>
      </div>
      <div class="right-section">
        <div class="status-list" style="width: 80%;">
          <div class="status-item">
            <div class="status-label">
              <el-badge type="info" is-dot />
              未完成
            </div>
            <div class="status-value">{{ apiData.unfinished }}</div>
          </div>
          <div class="status-item">
            <div class="status-label">
              <el-badge type="success" is-dot />
              已完成
            </div>
            <div class="status-value">{{ apiData.finished }}</div>
          </div>
        </div>
      </div>
    </div>
    <!-- 检测申请使用新样式 -->
    <div v-else-if="pluginApiType === 'test_application'" class="application-status-wrap">
      <div class="left-section">
        <div class="icon-container">
          <i class="cr-icon-jcsq" style="font-size: 32px;color:var(--color-600)"/>
        </div>
        <div class="total-section">
          <div class="total-number">{{ apiData.total }}</div>
          <div class="total-label">总数量</div>
        </div>
      </div>
      <div class="right-section">
        <div class="status-list" style="width: 80%;">
          <div class="status-item">
            <div class="status-label">
              <el-badge type="info" is-dot />
              未完成
            </div>
            <div class="status-value">{{ apiData.unfinished }}</div>
          </div>
          <div class="status-item">
            <div class="status-label">
              <el-badge type="success" is-dot />
              已完成
            </div>
            <div class="status-value">{{ apiData.finished }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 未完成检测项目使用新样式 -->
    <div v-else-if="pluginApiType === 'unfinished_test_project'" class="unfinished-project-wrap">
      <div class="left-section">
        <div class="unfinished-project-left-section">
          <div class="total-wrap">
            <div class="icon-container">
              <i class="cr-icon-wwcjcxm" style="font-size:34px;color:var(--color-600)"/>
            </div>
            <div class="total-section">
              <div class="total-number">{{ apiData.total }}</div>
              <div class="total-label">总数量</div>
            </div>
          </div>
          <div class="data-special">
            <div v-for="(item, index) in leftStatusList" :key="index" class="status-item">
              <div class="status-label">
                <el-badge :class="getStatusClass(item.status)" class="status-dot" is-dot />
                {{ item.status }}
              </div>
              <div class="status-value">{{ item.count || 0 }}</div>
            </div>
          </div>
        </div>
      </div>
      <div class="right-section">
        <div class="status-list">
          <div v-for="(item, index) in rightStatusList" :key="index" class="status-item">
            <div class="status-label">
              <el-badge :class="getStatusClass(item.status)" class="status-dot" is-dot />
              {{ item.status }}
            </div>
            <div class="status-value">{{ item.count || 0 }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 我参与的 -->
    <div v-else-if="pluginApiType === 'participate'" class="participate-wrap">
      <div
        v-for="item of resourcesConfig.options"
        :key="item.label"
        class="quantity-item"
      >
        <div class="value">
          <div class="custom-icon">
            <i :class="item.icon"/>
          </div>
          <template>
            {{ apiData[item] }}
            {{ apiData[item.countKey] }}
          </template>
        </div>
        <div class="label">{{ item.label }}</div>
      </div>
    </div>

    <!-- 其他情况使用原有样式 -->
    <div v-else class="plugin-quantity-wrap">
      <div class="plugin-quantity-content">
        <div
          v-for="item of resourcesConfig.options"
          :key="item.label"
          class="quantity-item"
        >
          <template v-if="!['single_drill_level', 'team_drill_level'].includes(pluginApiType)">
            <!-- 图标 -->
            <span v-if="showIcons" class="icon-view">
              <svg
                v-if="['individualDrillNum', 'individualNodeScale'].includes(item.countKey)"
                t="1711523064406"
                class="icon"
                viewBox="0 0 1024 1024"
                version="1.1"
                xmlns="http://www.w3.org/2000/svg"
                p-id="4467"
                width="200"
                height="200"
              >
                <path
                  d="M718.421333 593.664c81.28 31.146667 134.912 88.661333 134.912 175.658667v90.581333c0 43.52-35.285333 78.762667-78.762666 78.762667H249.429333A78.762667 78.762667 0 0 1 170.666667 859.904V769.28c0-87.04 53.632-144.512 134.912-175.658667a37.077333 37.077333 0 0 1 39.850666 8.746667c4.906667 5.034667 9.130667 9.088 12.714667 12.16A235.392 235.392 0 0 0 512 671.488a235.434667 235.434667 0 0 0 157.781333-60.373333c2.56-2.304 5.546667-5.205333 8.96-8.704a36.906667 36.906667 0 0 1 39.68-8.746667zM512 128a196.906667 196.906667 0 0 1 196.906667 196.906667V415.573333a196.906667 196.906667 0 0 1-393.813334 0V324.906667A196.906667 196.906667 0 0 1 512 128z"
                  fill="#515151"
                  p-id="4468"
                />
              </svg>
              <svg
                v-if="['groupDrillNum', 'groupNodeScale'].includes(item.countKey)"
                t="1711523366227"
                class="icon"
                viewBox="0 0 1024 1024"
                version="1.1"
                xmlns="http://www.w3.org/2000/svg"
                p-id="5793"
                width="200"
                height="200"
              >
                <path
                  d="M774.4 742.4c0-44.8-12.8-89.6-38.4-121.6C691.2 569.6 640 544 576 544c-38.4 0-76.8 0-115.2 0-44.8 0-83.2 12.8-121.6 38.4-44.8 32-70.4 76.8-76.8 128-6.4 57.6 0 121.6-6.4 185.6 6.4 0 19.2 6.4 25.6 6.4 76.8 19.2 147.2 38.4 224 38.4 44.8 0 96 0 140.8-6.4 38.4-6.4 76.8-19.2 115.2-32 6.4 0 6.4-6.4 6.4-12.8C774.4 838.4 774.4 787.2 774.4 742.4z"
                  p-id="5794"
                  fill="#515151"
                />
                <path
                  d="M358.4 531.2c12.8-6.4 19.2-6.4 32-12.8-38.4-38.4-57.6-76.8-57.6-128-44.8 0-89.6 0-134.4 0C172.8 390.4 147.2 396.8 128 403.2 57.6 428.8 0 499.2 0 576c0 51.2 0 102.4 0 160 0 6.4 0 6.4 6.4 12.8 51.2 12.8 102.4 25.6 153.6 32 19.2 0 44.8 6.4 64 6.4 0-12.8 0-25.6 0-38.4 0-19.2 0-38.4 6.4-57.6C249.6 614.4 294.4 563.2 358.4 531.2z"
                  p-id="5795"
                  fill="#515151"
                />
                <path
                  d="M1024 588.8c0-25.6-6.4-57.6-19.2-83.2-38.4-76.8-96-115.2-179.2-115.2-121.6 0 0 0-121.6 0 0 51.2-19.2 96-57.6 128 6.4 0 12.8 6.4 12.8 6.4 32 12.8 64 32 89.6 57.6 38.4 44.8 57.6 96 57.6 153.6 0 12.8 0 32 0 44.8 32-6.4 57.6-6.4 89.6-12.8 38.4-6.4 83.2-19.2 121.6-38.4 6.4 0 6.4-6.4 6.4-12.8C1024 684.8 1024 633.6 1024 588.8z"
                  p-id="5796"
                  fill="#515151"
                />
                <path
                  d="M518.4 537.6c83.2 0 153.6-70.4 147.2-153.6 0-83.2-70.4-147.2-147.2-147.2-83.2 0-153.6 64-153.6 147.2C371.2 467.2 435.2 537.6 518.4 537.6z"
                  p-id="5797"
                  fill="#515151"
                />
                <path
                  d="M704 371.2C723.2 377.6 742.4 384 768 384c83.2 0 153.6-70.4 147.2-153.6 0-83.2-70.4-147.2-147.2-147.2-83.2 0-153.6 64-153.6 147.2C665.6 256 697.6 307.2 704 371.2z"
                  p-id="5798"
                  fill="#515151"
                />
                <path
                  d="M256 384c25.6 0 57.6-6.4 76.8-19.2 6.4-51.2 32-96 70.4-121.6 0 0 0-6.4 0-6.4 0-83.2-70.4-147.2-147.2-147.2-83.2 0-153.6 64-153.6 147.2C102.4 313.6 172.8 384 256 384z"
                  p-id="5799"
                  fill="#515151"
                />
              </svg>
            </span>
            <div v-else-if="pluginApiType === 'test_project_count'" class="custom-icon">
              <i class="el-icon-document"/>
            </div>
            <div v-else-if="pluginApiType === 'test_pass_rate'" class="custom-icon">
              <i class="el-icon-pie-chart"/>
            </div>
            <div v-else-if="pluginApiType === 'test_application'" class="custom-icon">
              <i class="el-icon-document-checked"/>
            </div>
            <div v-else-if="pluginApiType === 'unfinished_test_project'" class="custom-icon">
              <i class="el-icon-document-remove"/>
            </div>
          </template>
          <div class="value">
            <template>
              {{ apiData[item.countKey] }} {{ getUnit }}
              <span v-if="item.countKey === 'thisYear' && showTrend" :class="{'trend-up': isTrendUp, 'trend-down': !isTrendUp}" class="trend">
                {{ apiData.trend }}
              </span>
            </template>
          </div>
          <div class="label">{{ item.label }}</div>
          <el-rate
            v-if="['single_drill_level', 'team_drill_level'].includes(pluginApiType)"
            :value="rateVal(item)"
            :max="3"
            class="rate"
            disabled
            disabled-void-icon-class="el-icon-star-hide"
          />
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="scss">
.participate-wrap{
  display: flex;
  flex: 1;
  align-items: center;
  justify-content: center;
  .quantity-item {
      flex: 1;
      text-align: center;
      border-right: solid 1px #eceef0;
      display: flex;
      flex-direction: column;
      align-items: center;
      &:last-child {
        border-right: none;
      }
      .custom-icon {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 70px;
        height: 70px;
        border-radius: 50%;
        i {
          font-size: 36px;
          color: var(--color-600);
        }
      }
      .icon-view {
        display: flex;
        justify-content: center;
        align-items: center;
        background: #f5f7fa;
        width: 70px;
        height: 70px;
        border-radius: 50%;
        color: #707275;
        font-size: 20px;
        font-weight: 800;
        margin-bottom: 16px;
        .icon {
          width: 36px;
          height: 36px;
          display: inline-block;
        }
      }
      .value {
        margin-bottom: 10px;
        font-size: 20px;
        font-weight: 600;
        color: #333;
        .custom-icon {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 70px;
          height: 70px;
          border-radius: 50%;
          i {
            font-size: 36px;
            color: var(--color-600);
          }
        }
        a {
          color: #333;
          &:hover {
            color: var(--color-600);
          }
        }
        .trend {
          font-size: 14px;
          font-weight: normal;
        }
        .trend-up {
          color: #67C23A;
        }
        .trend-down {
          color: #F56C6C;
        }
      }
      .label {
        font-size: 14px;
        color: var(--neutral-600);
      }
      .rate {
        margin-top: 10px;
        .el-rate__icon {
          margin-right: 0;
          font-size: 24px;
          &.el-icon-star-hide {
            display: none;
          }
        }
      }
    }
}
.plugin-quantity-wrap {
  display: flex;
  flex: 1;
  align-items: center;
  justify-content: center;

  .plugin-quantity-content {
    flex: 1;
    display: flex;
    flex-direction: row;
    align-items: center;
    .quantity-item {
      flex: 1;
      text-align: center;
      border-right: solid 1px #eceef0;
      display: flex;
      flex-direction: column;
      align-items: center;
      &:last-child {
        border-right: none;
      }
      .custom-icon {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 70px;
        height: 70px;
        border-radius: 50%;
        i {
          font-size: 36px;
          color: var(--color-600);
        }
      }
      .icon-view {
        display: flex;
        justify-content: center;
        align-items: center;
        background: #f5f7fa;
        width: 70px;
        height: 70px;
        border-radius: 50%;
        color: #707275;
        font-size: 20px;
        font-weight: 800;
        margin-bottom: 16px;
        .icon {
          width: 36px;
          height: 36px;
          display: inline-block;
        }
      }
      .value {
        margin-bottom: 10px;
        font-size: 20px;
        font-weight: 600;
        color: #333;
        a {
          color: #333;
          &:hover {
            color: var(--color-600);
          }
        }
        .trend {
          font-size: 14px;
          font-weight: normal;
        }
        .trend-up {
          color: #67C23A;
        }
        .trend-down {
          color: #F56C6C;
        }
      }
      .label {
        font-size: 14px;
        color: var(--neutral-600);
      }
      .rate {
        margin-top: 10px;
        .el-rate__icon {
          margin-right: 0;
          font-size: 24px;
          &.el-icon-star-hide {
            display: none;
          }
        }
      }
    }
  }
}

/* 检测申请样式 */
.application-status-wrap {
  display: flex;
  align-items: center;
  height: 100%;
  padding: 20px;
  padding-top: 0;

  .left-section {
    display: flex;
    align-items: center;
    margin-right: 30px;

    .icon-container {
      width: 50px;
      height: 50px;
      border-radius: 6px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 15px;

      i {
        font-size: 24px;
        color: #606266;
      }
    }

    .total-section {
      .total-number {
        font-size: 28px;
        font-weight: bold;
        color: #303133;
      }

      .total-label {
        font-size: 14px;
        color: #606266;
      }
    }
  }

  .right-section {
    flex: 1;
    // display: flex;
    // flex-direction: column;
    width: 80%;
    text-align: -webkit-right;

    .status-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;
      border-bottom: 1px solid #dbdde0;
      padding-bottom: 5px;
      &:last-child {
        margin-bottom: 0;
      }

      .status-label {
        display: flex;
        align-items: center;

        .status-dot {

          &.unfinished {
            .el-badge__content.is-dot{
              background-color: #F56C6C;
            }
          }

          &.finished {
            .el-badge__content.is-dot{
              background-color: #67C23A;
            }
          }
        }
      }

      .status-value {
        font-size: 14px;
        font-weight: bold;
        color: #303133;
      }
    }
  }
}

/* 未完成检测项目样式 */
.unfinished-project-wrap {
  display: flex;
  align-items: center;
  height: 100%;
  padding: 20px;
  padding-top: 0;

  .left-section {
    display: flex;
    flex: 0.8;
    align-items: center;

    .unfinished-project-left-section{
      display: flex;
      flex-direction: column;
      width: 100%;

      .total-wrap {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
      }

      .data-special {
        width: 100%;

        .status-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 10px;
          border-bottom: 1px solid #dbdde0;
          padding-bottom: 5px;
          width: 100%;

          &:last-child {
            margin-bottom: 0;
          }

          .status-label {
            display: flex;
            align-items: center;

            .status-dot {

              &.data-delivery {
                .el-badge__content.is-dot{
                  background-color: #73c0de;
                }
              }

              &.waiting-approval{
                .el-badge__content.is-dot{
                  background-color: #ee6666 ;
                }
              }

              &.env-deployment {
                .el-badge__content.is-dot{
                  background-color: #91cc75;
                }
              }

              &.pending-test {
                .el-badge__content.is-dot{
                  background-color: #3ba272;
                }
              }

              &.testing {
                .el-badge__content.is-dot{
                  background-color: #fac858;
                }
              }

              &.suspended {
                .el-badge__content.is-dot{
                  background-color: #5470c6;
                }
              }
            }
          }

          .status-value {
            font-size: 14px;
            font-weight: bold;
            color: #303133;
          }
        }
      }
    }

    .icon-container {
      width: 50px;
      height: 50px;
      border-radius: 6px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 15px;

      i {
        font-size: 24px;
        color: #606266;
      }
    }

    .total-section {
      .total-number {
        font-size: 28px;
        font-weight: bold;
        color: #303133;
      }

      .total-label {
        font-size: 14px;
        color: #606266;
      }
    }
  }

  .right-section {
    flex: 1;
    width: 80%;
    text-align: -webkit-right;
    .status-list {
      width: 80%;
      .status-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
        border-bottom: 1px solid #dbdde0;
        padding-bottom: 5px;
        &:last-child {
          margin-bottom: 0;
        }

        .status-label {
          display: flex;
          align-items: center;

          .status-dot {

            &.data-delivery {
              .el-badge__content.is-dot{
                background-color: #73c0de;
              }
            }

            &.waiting-approval{
              .el-badge__content.is-dot{
                background-color: #ee6666 ;
              }
            }

            &.env-deployment {
              .el-badge__content.is-dot{
                background-color: #91cc75;
              }
            }

            &.pending-test {
              .el-badge__content.is-dot{
                background-color: #3ba272;
              }
            }

            &.testing {
              .el-badge__content.is-dot{
                background-color: #fac858;
              }
            }

            &.suspended {
              .el-badge__content.is-dot{
                background-color: #5470c6;
              }
            }
          }
        }

        .status-value {
          font-size: 14px;
          font-weight: bold;
          color: #303133;
        }
      }
    }
  }
}
</style>
<script>
import { problemCount, projectCount, unFinishProject, detectionApplicationCount, participatedProjectsTask, managerParticipatedProjectsTask, manufacturerProject } from '@/api/testing/testingOverview'
import pluginMixin from './mixin_plugin.js'

export default {
  mixins: [pluginMixin],
  props: {
    data: Object,
    processId: {
      type: [Number, String],
      default: ''
    },
    currentRole: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      noDataImg: require('@/packages/table-view/nodata.png'),
      showIcons: false, // 不显示旧的图标
      showTrend: false, // 是否显示趋势
      isTrendUp: true, // 趋势是否上升
      rightStatusList: [], // 未完成检测项目右侧list
      leftStatusList: [] // 未完成检测项目左侧list
    }
  },
  computed: {
    getUnit() {
      return this.resourcesConfig.unit
    }
  },
  watch: {
    processId: {
      handler(newVal) {
        this.getData()
      },
      immediate: true
    },
    currentRole: {
      handler(newVal) {
        this.getData()
      },
      immediate: true
    }
  },
  methods: {
    changeFontSize(item) {
      if (item.icon === 'cr-icon-jcsm-wtqn' && item.label === '今年') {
        return 'font-size: 38px;transform: rotateY(180deg);'
      }
      if (item.icon === 'cr-icon-jcsm-wtqn' && item.label === '去年') {
        return 'font-size: 38px;'
      }
      if (item.icon === 'cr-icon-wtjn') {
        return 'font-size: 32px;'
      }
      if (item.icon === 'cr-icon-jcsmzs') {
        return 'font-size: 34px;'
      } else {
        return 'font-size: 36px;'
      }
    },
    getStatusClass(status) {
      if (status === '资料待送审') {
        return 'data-delivery'
      } else if (status === '资料待审核') {
        return 'waiting-approval'
      } else if (status === '环境待部署') {
        return 'env-deployment'
      } else if (status === '待测试') {
        return 'pending-test'
      } else if (status === '测试中') {
        return 'testing'
      } else if (status === '已挂起') {
        return 'suspended'
      } else if (status === '未完成') {
        return 'unfinished'
      } else if (status === '已完成') {
        return 'finished'
      }
      return 'default'
    },
    getData: async function(hideloading) {
      if (!hideloading) {
        this.loading = true
      }
      // 延迟一下模拟网络请求
      // 针对不同的数据类型，加载不同的模拟数据
      const apiType = this.data.pluginConfig.pluginApiKey
      // 判断是否显示趋势，并设置趋势方向
      if (apiType === 'test_project_count') {
        // 检测项目数量统计
        await this.callRealApi(projectCount)
      } else if (apiType === 'test_pass_rate') {
        // 问题数量统计
        await this.callRealApi(problemCount)
      } else if (apiType === 'test_application') {
        // 检测申请
        await this.callRealApi(detectionApplicationCount)
      } else if (apiType === 'vendor_test_project') {
        await this.callRealApi(manufacturerProject)
      } else if (apiType === 'unfinished_test_project') {
        // 未完成检测项目
        await this.callRealApi(unFinishProject)
        this.showTrend = false
      } else if (apiType === 'participate') {
        // 我参与的
        if (this.currentRole === '181251') {
          await this.callRealApi(managerParticipatedProjectsTask)
        } else if (this.currentRole === '181253') {
          await this.callRealApi(participatedProjectsTask)
        }
        this.showTrend = false
      } else {
        // 如果是其他类型，尝试调用实际API
        this.callRealApi(apiType)
        return
      }

      this.loading = false
    },

    callRealApi(apiType) {
      switch (this.data.pluginConfig.pluginApiKey) {
        case 'test_project_count':
          {
            const params = {
              processId: this.processId,
              roleId: this.currentRole
            }
            apiType(params).then(res => {
              if (res.code == 0) {
                this.apiData = res.data
                this.showTrend = true
                if (this.apiData.trend === 1) {
                  this.isTrendUp = true
                } else {
                  this.isTrendUp = false
                }
              }
            })
          }
          break
        case 'test_pass_rate':
          {
            const params = {
              processId: this.processId,
              roleId: this.currentRole
            }
            apiType(params).then(res => {
              if (res.code == 0) {
                this.apiData = res.data
                this.showTrend = true
                if (this.apiData.trend === 1) {
                  this.isTrendUp = true
                } else {
                  this.isTrendUp = false
                }
              }
            })
          }
          break
        case 'test_application':
          {
            const params = {
              processId: this.processId
            }
            apiType(params).then(res => {
              if (res.code == 0) {
                this.apiData = res.data
                this.showTrend = false
              }
            })
          }
          break
        case 'unfinished_test_project':
          {
            const params = {
              processId: this.processId,
              roleId: this.currentRole
            }
            apiType(params).then(res => {
              if (res.code == 0) {
                this.apiData = res.data
                this.rightStatusList = res.data.statusList.filter(item => {
                  return item.status !== '待测试' && item.status !== '已挂起'
                })
                this.leftStatusList = res.data.statusList.filter(item => {
                  return item.status === '待测试' || item.status === '已挂起'
                })
                this.showTrend = false
              }
            })
          }
          break
        case 'vendor_test_project':
          {
            const params = {
              processId: this.processId
            }
            apiType(params).then(res => {
              if (res.code == 0) {
                this.apiData = res.data
                this.showTrend = false
              }
            })
          }
          break
        case 'participate':
          {
            const params = {
              processId: this.processId
            }
            apiType(params).then(res => {
              if (res.code == 0) {
                this.apiData = res.data
                this.showTrend = false
              }
            })
          }
          break
        default:
          this.timeParam = null
      }
    },

    rateVal(item) {
      const levelValue = { 初级: 1, 中级: 2, 高级: 3 }
      return levelValue[item.label]
    }
  }
}
</script>
