<template>
  <div class="device-form">
    <el-form ref="form" :model="formData" :rules="rules" label-width="100px">
      <el-form-item label="设备名称" prop="deviceName" class="required-field">
        <el-input v-model.trim="formData.deviceName" placeholder="请输入" />
      </el-form-item>
      <el-form-item label="是否为工作站" prop="workstation">
        <el-switch v-model="formData.workstation" active-value="1" inactive-value="0"/>
      </el-form-item>

      <el-form-item label="镜像名称" prop="imageId" class="required-field">
        <el-tag
          v-if="formData.imageId"
          :disable-transitions="true"
          closable
          @click="drawerName = 'selectImage'"
          @close="formData.imageId = null">
          {{ imageName }}
        </el-tag>
        <el-button v-else type="ghost" @click="drawerName = 'selectImage'">选择镜像</el-button>
      </el-form-item>

      <el-form-item label="CPU" prop="cpuCount" class="required-field">
        <div class="number-wrap">
          <div class="number-input-wrapper">
            <el-button class="number-btn" @click="decreaseCpu"><i class="el-icon-minus"/></el-button>
            <el-input
              v-model="formData.cpuCount"
              class="number-input"
              @input="validateCpuInput"
            />
            <el-button class="number-btn" @click="increaseCpu"><i class="el-icon-plus"/></el-button>
          </div>
          <div class="unit">核</div>
        </div>
      </el-form-item>

      <el-form-item label="内存" prop="memSize" class="required-field">
        <div class="number-wrap">
          <div class="number-input-wrapper">
            <el-button class="number-btn" @click="decreaseMemory"><i class="el-icon-minus"/></el-button>
            <el-input
              v-model="formData.memSize"
              class="number-input"
              @input="validateMemoryInput"
            />
            <el-button class="number-btn" @click="increaseMemory"><i class="el-icon-plus"/></el-button>
          </div>
          <el-select v-model="formData.memoryUnit" style="width: 70px !important;" @change="handleMemoryUnitChange">
            <el-option label="GB" value="GB"/>
            <el-option label="MB" value="MB"/>
          </el-select>
        </div>
      </el-form-item>

      <el-form-item label="系统盘大小" prop="sysDiskSize">
        <div class="number-wrap">
          <div class="number-input-wrapper">
            <el-button class="number-btn" @click="decreaseSystem"><i class="el-icon-minus"/></el-button>
            <el-input
              v-model="formData.sysDiskSize"
              class="number-input"
              @input="validateSystemDiskInput"
            />
            <el-button class="number-btn" @click="increaseSystem"><i class="el-icon-plus"/></el-button>
          </div>
          <div class="unit">GB</div>
        </div>
      </el-form-item>

      <el-form-item label="数据盘大小" prop="dataDiskSize">
        <div class="number-wrap">
          <div class="number-input-wrapper">
            <el-button class="number-btn" @click="decreaseData"><i class="el-icon-minus"/></el-button>
            <el-input
              v-model="formData.dataDiskSize"
              class="number-input"
              @input="validateDataDiskInput"
            />
            <el-button class="number-btn" @click="increaseData"><i class="el-icon-plus"/></el-button>
          </div>
          <div class="unit">GB</div>
        </div>
      </el-form-item>
    </el-form>

    <!-- 侧拉弹窗 -->
    <el-drawer
      :title="titleMapping[drawerName]"
      :visible.sync="drawerShow"
      :size="drawerWidth"
      :modal="false"
      @close="drawerClose"
    >
      <transition name="el-fade-in-linear">
        <component
          :is="drawerName"
          :name="drawerName"
          @close="drawerClose"
          @call="drawerConfirmCall"
        />
      </transition>
    </el-drawer>

    <div class="drawer-footer">
      <el-button type="primary" @click="confirm">确定</el-button>
      <el-button type="text" @click="close">取消</el-button>
    </div>
  </div>
</template>

<script>
import mixinsActionMenu from '@/packages/mixins/action_menu.js'
import validate from '@/packages/validate'
import selectImage from './select-image.vue'

export default {
  name: 'AddVirtualMachine',
  components: {
    selectImage
  },
  mixins: [mixinsActionMenu],
  props: {
    projectId: {
      type: [String, Number],
      default: ''
    },
    virtualBtnType: {
      type: String,
      default: ''
    },
    // 编辑虚拟机数据
    editVirtualData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    // 自定义验证器：正整数，范围1-100000
    const validateCpu = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入CPU核心数'))
      } else if (!/^[1-9]\d*$/.test(value)) {
        this.formData.cpuCount = ''
        callback(new Error('请输入正整数'))
      } else if (Number(value) < 1 || Number(value) > 100000) {
        callback(new Error('CPU核心数范围为1-100000'))
      } else {
        callback()
      }
    }

    // 自定义验证器：正整数，范围1-10240
    const validateMemory = (rule, value, callback) => {
      const unit = this.formData.memoryUnit
      const min = 1
      const max = unit === 'MB' ? 10485760 : 10240
      if (!value) {
        callback(new Error('请输入内存大小'))
      } else if (!/^[1-9]\d*$/.test(value)) {
        this.formData.memSize = ''
        callback(new Error('请输入正整数'))
      } else if (Number(value) < min || Number(value) > max) {
        callback(new Error(`内存大小范围为${min}-${max}${unit}`))
      } else {
        callback()
      }
    }

    // 自定义验证器：系统盘大小，范围1-10240
    const validateSystemDisk = (rule, value, callback) => {
      if (value === '' || value === null || value === undefined) {
        callback(new Error('请输入系统盘大小'))
      } else if (!/^[1-9]\d*$/.test(value)) {
        this.formData.sysDiskSize = 1
        callback(new Error('请输入正整数'))
      } else if (Number(value) < 1 || Number(value) > 10240) {
        callback(new Error('系统盘大小范围为1-10240'))
      } else {
        callback()
      }
    }

    // 自定义验证器：数据盘大小，范围1-10240
    const validateDataDisk = (rule, value, callback) => {
      if (value === '' || value === null || value === undefined) {
        callback()
      } else if (!/^[1-9]\d*$/.test(value)) {
        this.formData.dataDiskSize = ''
        callback(new Error('请输入正整数'))
      } else if (Number(value) < 1 || Number(value) > 10240) {
        callback(new Error('数据盘大小范围为1-10240'))
      } else {
        callback()
      }
    }

    return {
      loading: false,
      imageName: '',
      drawerAction: ['selectImage'], // 需要侧拉打开的操作
      titleMapping: {
        'selectImage': '选择镜像'
      },
      formData: {
        deviceName: '',
        imageId: '',
        cpuCount: 1,
        memSize: 1,
        memoryUnit: 'GB',
        sysDiskSize: 1,
        dataDiskSize: null,
        workstation: '0'
      },
      rules: {
        deviceName: [
          validate.required(),
          validate.name_64_char
        ],
        imageId: [
          { required: true, message: '请选择镜像', trigger: 'change' }
        ],
        cpuCount: [
          { required: true, message: '请设置CPU核心数', trigger: 'blur' },
          { validator: validateCpu, trigger: 'blur' }
        ],
        memSize: [
          { required: true, message: '请设置内存大小', trigger: 'blur' },
          { validator: validateMemory, trigger: 'blur' }
        ],
        sysDiskSize: [
          { required: true, message: '请输入系统盘大小' },
          { validator: validateSystemDisk, trigger: 'blur' }
        ],
        dataDiskSize: [
          { validator: validateDataDisk, trigger: 'blur' }
        ]
      }
    }
  },
  watch: {
    'formData.imageId': {
      handler(val) {
        if (val) {
          this.$refs.form.clearValidate('imageId')
        }
      }
    }
  },
  created() {
    // 如果是编辑模式，初始化表单数据
    if (this.virtualBtnType === 'edit' && this.editVirtualData) {
      const data = JSON.parse(JSON.stringify(this.editVirtualData))
      // 保存原始索引，用于无id数据的更新
      this.formData._index = data._index

      this.formData.deviceName = data.deviceName || data.name
      this.formData.imageId = data.imageId
      this.formData.imageName = data.imageName
      this.formData.cpuCount = data.cpu
      this.formData.memSize = data.memory
      this.formData.memoryUnit = data.memoryUnit
      this.formData.sysDiskSize = data.system
      this.formData.dataDiskSize = data.data
      this.formData.workstation = data.workstation
      this.formData.imageId = data.imageId
      this.imageName = data.imageName
    }
  },
  methods: {
    // 验证CPU输入
    validateCpuInput(value) {
      if (value === '') return
      if (!/^[1-9]\d*$/.test(value)) {
        this.formData.cpuCount = ''
      } else if (Number(value) > 100000) {
        this.formData.cpuCount = 100000
      }
    },

    // 验证内存输入
    validateMemoryInput(value) {
      if (value === '') return
      const unit = this.formData.memoryUnit
      const max = unit === 'MB' ? 10485760 : 10240
      if (!/^[1-9]\d*$/.test(value)) {
        this.formData.memSize = ''
      } else if (Number(value) > max) {
        this.formData.memSize = max
      }
    },

    // 验证系统盘输入
    validateSystemDiskInput(value) {
      if (value === '') return
      if (!/^[1-9]\d*$/.test(value)) {
        this.formData.sysDiskSize = ''
      } else if (Number(value) > 10240) {
        this.formData.sysDiskSize = 10240
      }
    },

    // 验证数据盘输入
    validateDataDiskInput(value) {
      if (value === '') return
      if (!/^[1-9]\d*$/.test(value)) {
        this.formData.dataDiskSize = null
      } else if (Number(value) > 10240) {
        this.formData.dataDiskSize = 10240
      }
    },
    drawerConfirmCall(type, data) {
      if (type === 'close') {
        this.drawerClose()
      } else if (type === 'select_image') {
        this.formData.imageId = data.value
        this.imageName = data.label
        this.drawerClose()
      }
    },
    increaseCpu() {
      if (this.formData.cpuCount >= 1 && this.formData.cpuCount < 100000) {
        this.formData.cpuCount = Number(this.formData.cpuCount) + 1
      }
    },
    decreaseCpu() {
      if (this.formData.cpuCount > 1) {
        this.formData.cpuCount = Number(this.formData.cpuCount) - 1
      }
    },
    increaseMemory() {
      const unit = this.formData.memoryUnit
      const max = unit === 'MB' ? 10485760 : 10240
      if (this.formData.memSize >= 1 && this.formData.memSize < max) {
        this.formData.memSize = Number(this.formData.memSize) + 1
      }
    },
    decreaseMemory() {
      if (this.formData.memSize > 1) {
        this.formData.memSize = Number(this.formData.memSize) - 1
      }
    },
    increaseSystem() {
      if (this.formData.sysDiskSize >= 1 && this.formData.sysDiskSize < 10240) {
        this.formData.sysDiskSize = Number(this.formData.sysDiskSize) + 1
      }
    },
    decreaseSystem() {
      if (this.formData.sysDiskSize > 1) {
        this.formData.sysDiskSize = Number(this.formData.sysDiskSize) - 1
      }
    },
    increaseData() {
      if (this.formData.dataDiskSize >= 1 && this.formData.dataDiskSize < 10240) {
        this.formData.dataDiskSize = Number(this.formData.dataDiskSize) + 1
      }
    },
    decreaseData() {
      if (this.formData.dataDiskSize > 1) {
        this.formData.dataDiskSize = Number(this.formData.dataDiskSize) - 1
      }
    },
    handleMemoryUnitChange() {
      const unit = this.formData.memoryUnit
      const max = unit === 'MB' ? 10485760 : 10240
      if (Number(this.formData.memSize) > max) {
        this.formData.memSize = max
      }
    },
    close() {
      this.$emit('close')
    },
    confirm() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.loading = true

          if (this.virtualBtnType == 'add') {
            // 添加模式：构造返回数据并触发回调
            const returnData = {
              id: null, // 临时ID将在父组件中生成
              deviceName: this.formData.deviceName,
              imageName: this.imageName,
              imageId: this.formData.imageId,
              cpu: this.formData.cpuCount,
              memory: this.formData.memSize,
              memoryUnit: this.formData.memoryUnit,
              system: this.formData.sysDiskSize,
              data: this.formData.dataDiskSize,
              workstation: this.formData.workstation
            }
            this.loading = false
            this.$message.success('添加虚拟机配置成功')
            this.$emit('call', 'confirm_virtual_machine', returnData)
          } else {
            // 编辑模式：只更新本地数据，不调用API
            const updatedData = {
              id: this.editVirtualData.id,
              deviceName: this.formData.deviceName,
              imageName: this.imageName,
              imageId: this.formData.imageId,
              cpu: this.formData.cpuCount,
              memory: this.formData.memSize,
              memoryUnit: this.formData.memoryUnit,
              system: this.formData.sysDiskSize,
              data: this.formData.dataDiskSize,
              workstation: this.formData.workstation,
              _index: this.formData._index
            }
            this.loading = false
            this.$message.success('编辑成功')
            this.$emit('call', 'confirm_virtual_machine', updatedData)
            this.close()
          }
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.device-form {
  padding: 20px;
  height: 100%;
  position: relative;
}

.required-field {
  ::v-deep .el-form-item__label:before {
    content: '*';
    color: #F56C6C;
    margin-right: 4px;
  }
}

.image-selector {
  width: 100%;
}

.number-wrap {
  display: flex;
  justify-content: flex-start;
}
.number-input-wrapper {
  display: flex;
  align-items: center;
  background: #fff;
  border: 1px solid #DCDFE6;
  border-radius: 4px;
  overflow: hidden;
  width: fit-content;
  height: 32px;
  margin: 3px 10px 0 0;
  .number-btn {
    width: 32px;
    height: 100%;
    border: none;
    border-radius: 0;
    background: #F5F7FA;
    font-size: 14px;
    padding: 0;
    margin: 0;
    color: #606266;
    box-shadow: none;
    transition: background 0.2s;
    &:first-child {
      border-right: 1px solid #DCDFE6;
    }
    &:last-child {
      border-left: 1px solid #DCDFE6;
    }
  }
  .number-input {
    width: 80px;
    margin: 0;
    ::v-deep.el-input__inner {
      border: none;
      box-shadow: none;
      text-align: center;
      height: 38px;
      line-height: 38px;
      font-size: 14px;
      padding: 0;
    }
  }
  .unit {
    margin-left: 10px;
    color: #606266;
  }
}

.drawer-footer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20px;
  background: #fff;
  border-top: 1px solid #e6e6e6;
}
</style>
