<template>
  <create-view :loading="loading" :title="isEditMode ? '重新送审' : '项目资料送审'">
    <div slot="content">
      <el-form
        ref="form"
        :model="formData"
        :rules="rules"
        label-position="left"
        label-width="100px"
      >
        <el-card class="info-card">
          <el-divider content-position="left">项目信息</el-divider>

          <div class="info-row">
            <div class="info-item">
              <span class="label">项目名称：</span>
              <span class="value">{{ projectDetail.name || "-" }}</span>
            </div>
            <div class="info-item">
              <span class="label">厂商：</span>
              <span class="value">{{ projectDetail.vendorName }}</span>
            </div>
          </div>

          <div class="info-row">
            <div class="info-item">
              <span class="label">检测产品：</span>
              <span class="value">{{ projectDetail.productName }}</span>
            </div>
            <div class="info-item">
              <span class="label">版本号：</span>
              <span class="value">{{ projectDetail.productVersion }}</span>
            </div>
          </div>
        </el-card>

        <el-card v-if="projectDetail.vendorStatus === 1" class="form-card">
          <el-divider content-position="left">检测范围</el-divider>

          <el-form-item label="测试任务" prop="testingTasks">
            <el-tree
              ref="testingTaskTree"
              :data="testingTasksData"
              :props="testingTaskProps"
              :key="treeKey"
              show-checkbox
              node-key="id"
              default-expand-all
              @check-change="handleTestingTaskCheckChange"
            />
          </el-form-item>
        </el-card>

        <el-card class="form-card">
          <el-divider content-position="left">环境资源</el-divider>

          <div class="env-tabs">
            <div class="label-block">检测环境</div>
            <div class="tab-buttons">
              <el-button
                :type="deployType === 'local' ? 'primary' : ''"
                @click="deployType = 'local'"
              >本平台部署</el-button
              >
              <el-button
                :type="deployType === 'external' ? 'primary' : ''"
                @click="deployType = 'external'"
              >外部部署</el-button
              >
            </div>
          </div>

          <!-- 本平台部署 -->
          <div v-if="deployType === 'local'" class="deploy-content">
            <div class="env-tabs">
              <div class="label-block">资源申请</div>
              <div class="tab-buttons">
                <el-button
                  :type="resourceType === 'virtual' ? 'primary' : ''"
                  @click="resourceType = 'virtual'"
                >虚拟机资源</el-button
                >
                <el-button
                  :type="resourceType === 'network' ? 'primary' : ''"
                  @click="resourceType = 'network'"
                >网络编排</el-button
                >
              </div>
            </div>
            <!-- 虚拟资源 -->
            <div v-if="resourceType === 'virtual'" class="resource-content">
              <div class="section-header">
                <span>虚拟机配置</span>
                <el-button
                  style="color: var(--color-600)"
                  type="text"
                  icon="el-icon-plus"
                  @click="showAddVirtualMachineDrawer('add', null)"
                >添加虚拟机配置</el-button
                >
              </div>
              <!-- 虚拟机配置列表 -->
              <virtual-table
                ref="virtualTableRef"
                :project-id="projectId"
                @removeVirtualMachine="removeVirtualMachine"
                @editVirtualMachine="showAddVirtualMachineDrawer"
              />
            </div>
            <!-- 网络编排 -->
            <div v-if="resourceType === 'network'" class="resource-content">
              <el-form-item
                label="仿真场景"
                label-width="100px"
                prop="scenarioId"
                class="required-item"
              >
                <span slot="label">
                  <span>仿真场景</span>
                  <el-tooltip transfer>
                    <i class="el-icon-warning-outline"/>
                    <div slot="content">
                      <div>请先在"场景管理"模块自行构建场景，再进行选择。如果您没有"场景管理"模块权限，请联系检测方或管理员进行开通。</div>
                    </div>
                  </el-tooltip>
                </span>
                <el-tag
                  v-if="formData.scenarioId"
                  :disable-transitions="true"
                  closable
                  style="margin-top: 4px;"
                  @click="drawerName = 'selectScenario'"
                  @close="formData.scenarioId = null"
                >
                  {{ formData.scenarioName }}
                </el-tag>
                <el-button
                  v-else
                  type="ghost"
                  @click="drawerName = 'selectScenario'"
                >选择场景</el-button
                >
              </el-form-item>

              <el-form-item
                label="场景描述"
                label-width="100px"
                prop="scenarioDescription"
                class="required-item"
              >
                <el-input
                  v-model.trim="formData.scenarioDescription"
                  :autosize="false"
                  :rows="4"
                  type="textarea"
                  style="width: 100%"
                  maxlength="255"
                  placeholder="请输入"
                />
              </el-form-item>
            </div>
          </div>

          <!-- 外部部署 -->
          <div v-if="deployType === 'external'" class="deploy-content">
            <div class="section-header">
              <span>设备信息</span>
              <el-button
                style="color: var(--color-600)"
                type="text"
                icon="el-icon-plus"
                @click="showAddDevice('add', null)"
              >添加设备信息</el-button
              >
            </div>
            <!-- 设备配置列表 -->
            <device-table
              ref="deviceTableRef"
              :project-id="projectId"
              @removeDevice="removeDevice"
              @editDevice="showAddDevice"
            />
            <el-form-item label="附件" label-width="100px" class="device-upload">
              <el-upload
                ref="upload"
                :action="uploadUrl"
                :headers="header"
                :file-list="deviceAttachmentList"
                :on-change="handleDeviceFileChange"
                :on-success="handleDeviceUploadSuccess"
                :on-remove="handleDeviceFileRemove"
                class="upload-demo"
              >
                <el-button type="text" icon="el-icon-plus" style="color: var(--color-600)">添加附件</el-button>
              </el-upload>
              <span style="color: #999; font-size: 12px;">上传详细的系统访问信息</span>
            </el-form-item>
          </div>
        </el-card>

        <el-card class="form-card" style="margin-bottom: 0;">
          <el-divider content-position="left">
            附件
            <el-tooltip transfer placement="top">
              <i class="el-icon-warning-outline" />
              <div slot="content">请先确认是否已选择测试任务信息，附件信息将关联测试任务</div>
            </el-tooltip>
          </el-divider>

          <div class="attachment-section">
            <div v-if="processTasks.length > 0" class="switch-group">
              <radio-group
                v-model="activeAttachmentType"
                :options="processTasks"
                label-key="processName"
                value-key="id"
                @change="val => activeAttachmentType = val"
              />
            </div>

            <div class="file-list-area">
              <div v-if="processTasks.length > 0" class="add-file-btn">
                <el-button
                  type="text"
                  icon="el-icon-plus"
                  style="color: var(--color-600)"
                  @click="addFile()"
                >添加附件</el-button
                >
              </div>
              <!-- 附件列表 -->
              <attachment-table
                ref="attachmentTableRef"
                :project-id="projectId"
                :active-attachment-type="activeAttachmentType"
                @previewFile="previewFile"
                @updateFile="updateFile"
                @removeFile="removeFile"
              />
            </div>
          </div>
        </el-card>
      </el-form>
      <!-- 侧拉弹窗 -->
      <el-drawer
        :title="titleMapping[drawerName]"
        :visible.sync="drawerShow"
        :size="drawerWidth"
        @close="drawerClose"
      >
        <transition name="el-fade-in-linear">
          <component
            :is="drawerName"
            :name="drawerName"
            :project-id="formData.id"
            :virtual-btn-type="virtualBtnType"
            :edit-virtual-data="editVirtualData"
            @close="drawerClose"
            @call="drawerConfirmCall"
          />
        </transition>
      </el-drawer>
      <!-- 中部弹窗 start-->
      <el-dialog
        :title="titleMapping[modalName]"
        :visible.sync="modalShow"
        :width="modalWidth"
        :destroy-on-close="true"
        append-to-body
        @close="modalClose"
      >
        <transition name="el-fade-in-linear">
          <component
            :is="modalName"
            :name="modalName"
            :data="selectItem"
            :device-btn-type="deviceBtnType"
            :edit-device-data="editDeviceData"
            :project-id="formData.id"
            @close="modalClose"
            @call="confirmCall"
          />
        </transition>
      </el-dialog>
      <!-- 中部弹窗 end-->
      <!-- 添加文件上传弹框 -->
      <el-dialog
        :visible.sync="uploadDialogVisible"
        :close-on-click-modal="false"
        class="upload-dialog"
        title="上传附件"
        width="520px"
      >
        <el-form
          ref="uploadForm"
          :model="uploadForm"
          style="margin-top: 10px;"
          label-width="80px"
        >
          <el-form-item label="关联任务">
            <span>{{ uploadForm.task }}</span>
          </el-form-item>
          <el-form-item label="附件" prop="file" required>
            <el-upload
              ref="upload"
              :action="uploadUrl"
              :headers="header"
              :on-change="handleFileChange"
              :on-remove="handleFileRemove"
              :on-success="handleUploadSuccess"
              :on-error="handleUploadError"
              :file-list="uploadForm.fileList"
              :before-upload="beforeUpload"
              multiple
              class="upload-demo"
            >
              <el-button
                slot="trigger" size="small" type="primary"
              >上传文件</el-button
              >
            </el-upload>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button
            type="text" @click="uploadDialogVisible = false"
          >取消</el-button
          >
          <el-button
            :loading="uploadLoading"
            type="primary"
            @click="
              uploadForm.currentUpdateFile.id
                ? submitUpdateFile()
                : submitUpload()
            "
          >确定</el-button
          >
        </div>
      </el-dialog>
    </div>
    <div slot="footer">
      <el-button type="text" @click="$router.go(-1)">取消</el-button>
      <el-button type="primary" @click="handleSubmit">确定</el-button>
    </div>
  </create-view>
</template>

<script>
import createView from '@/packages/create-view/index'
import { getToken } from '@/utils/auth'
import listModule from './config'
// import validate from '@/packages/validate'
import {
  getResourceApplyInfoApi,
  processTaskAPI,
  processTaskQueryAPI,
  projectTestApplyAPI,
  testingItemsDetailAPI
} from '@/api/testing/index'
import radioGroup from '@/components/commonRadioGroup/index.vue'
import CountPopover from '@/components/CountPopover/index'
import filePreview from '@/components/testing/utils/filePreview'
import mixinsActionMenu from '@/packages/mixins/action_menu.js'
import mixinsPageTable from '@/packages/mixins/page_table'
import tTableConfig from '@/packages/table-config/table-col-config.vue'
import tableTdMultiCol from '@/packages/table-config/table-td-multi-col.vue'
import tTableView from '@/packages/table-view/index.vue'
import { default as addDevice, default as editDevice } from '../create/add-device.vue'
import { default as addVirtualMachine, default as editVirtualMachine } from '../create/add-virtual-machine.vue'
import selectScenario from '../create/select-scenario.vue'
import attachmentTable from './components/attachmentTable.vue'
import deviceTable from './components/deviceTable.vue'
import virtualTable from './components/virtualTable.vue'
export default {
  name: 'TestingItemsSubmit',
  components: {
    createView,
    addVirtualMachine,
    editVirtualMachine,
    addDevice,
    editDevice,
    selectScenario,
    tTableView,
    tTableConfig,
    tableTdMultiCol,
    CountPopover,
    radioGroup,
    virtualTable,
    deviceTable,
    attachmentTable
  },
  mixins: [mixinsActionMenu, mixinsPageTable, filePreview],
  provide() {
    return {
      'testSubmitVm': this
    }
  },
  data() {
    return {
      drawerAction: ['addVirtualMachine', 'selectScenario'], // 需要侧拉打开的操作
      titleMapping: {
        addVirtualMachine: '添加虚拟机配置',
        editVirtualMachine: '编辑虚拟机配置',
        selectScenario: '选择场景',
        addDevice: '添加设备信息',
        editDevice: '编辑设备信息'
      },
      loading: false,
      deployType: 'local', // local-本平台部署, external-外部部署
      resourceType: 'virtual', // virtual-虚拟资源, network-网络编排
      activeAttachmentType: '', // 使用第一个任务的ID作为初始值
      attachmentList: [],
      deviceAttachmentList: [],
      checkedTaskIds: [], // 选中的任务id
      treeKey: 0, // 用于强制重新渲染树形组件
      isEditMode: false, // 是否为编辑模式
      applyId: '', // 资源申请ID
      projectDetail: {
        id: '',
        name: '',
        round: '',
        status: '',
        managerId: '',
        managerName: '',
        processId: '',
        processName: '',
        planBeginDate: '',
        actualBeginTime: '',
        actualEndTime: '',
        createBy: '',
        createByName: '',
        createAt: '',
        description: ''
      },
      formData: {
        id: '',
        deployType: 'local',
        resourceType: 'virtual',
        devices: [],
        virtualMachines: [],
        scenarioId: '',
        scenarioName: '',
        scenarioCategory: '',
        scenarioDescription: '',
        testingTasks: [] // 选中的检测任务ID
      },
      rules: {
        scenarioId: [
          { required: true, message: '请选择仿真场景', trigger: 'change' }
        ],
        scenarioDescription: [
          { required: true, message: '请输入场景描述', trigger: 'blur' }
        ]
      },
      uploadDialogVisible: false,
      uploadForm: {
        task: '',
        fileList: [],
        currentUpdateFile: {}
      },
      processTasks: [],
      uploadLoading: false,
      testingTasksData: [], // 检测任务树形数据
      testingTaskProps: {
        label: 'processName',
        children: 'children'
      },
      searchKeyList: [],
      fileColumnsObj: listModule.fileColumnsObj,
      fileColumnsViewArr: listModule.fileColumnsViewArr,
      editDeviceData: null,
      deviceBtnType: 'add',
      editVirtualData: null,
      virtualBtnType: 'add',
      maxFileSize: localStorage.getItem('maxFileSize'),
      taskIdListPostData: [] // 提交时需要发送的检测任务ID列表
    }
  },
  computed: {
    activeAttachmentTypeName() {
      // 查找当前选中的任务
      const selectedTask = this.processTasks.find(
        task => task.id === this.activeAttachmentType
      )
      if (selectedTask) {
        return selectedTask.processName
      }
    },
    header() {
      return {
        'Admin-Token': getToken()
      }
    },
    uploadUrl() {
      return '/api/testing/attachment/upload'
    }
  },
  watch: {
    activeAttachmentType(newVal) {
      // 当附件类型变化时，更新上传表单的任务名称
      const currentTask = this.processTasks.find(task => task.id === newVal)
      if (currentTask) {
        this.uploadForm.task = currentTask.processName
      } else {
        this.uploadForm.task = this.activeAttachmentTypeName
      }

      // 不需要调用getList方法，只需要确保子组件可以响应activeAttachmentType的变化
      // 子组件会根据activeAttachmentType自动显示对应的附件列表
    }
  },
  created() {
    // 初始化testingTasks数组
    if (!this.formData.testingTasks) {
      this.formData.testingTasks = []
    }
    const id = this.$route.params.id
    this.projectId = id
    this.formData.id = id
    this.loadNormalProjectDetail(id)
  },
  methods: {
    // 获取附件列表
    getList: function(showLoading = true, data) {
      // 改为本地数据处理
      if (this.$refs.attachmentTableRef) {
        this.$refs.attachmentTableRef.getList(showLoading)
        // 不再每次都调用initializeLocalData，由watch监听处理
      }
    },
    async getProjectDetail(id) {
      // 检查是否为编辑模式（auditStatus为2表示审核不通过，需要重新编辑）
      this.isEditMode = this.$route.query.auditStatus == '2'
      this.applyId = this.$route.query.applyId
      if (this.isEditMode && this.applyId) {
        // 编辑模式：根据资源申请ID获取详情
        const res = await getResourceApplyInfoApi(this.applyId)
        if (res.data && res.data.code === 0) {
          // 保存申请详情数据
          const applyDetail = res.data.data || {}
          // 根据申请详情设置环境类型和资源类型
          if (applyDetail.projectEnvVO && applyDetail.projectEnvVO.envType !== undefined) {
            this.deployType = applyDetail.projectEnvVO.envType === 0 ? 'local' : 'external'
            // 外部部署添加的附件
            if (applyDetail.projectEnvVO.envAttachments && applyDetail.projectEnvVO.envType === 1) {
              this.deviceAttachmentList = applyDetail.projectEnvVO.envAttachments.map(item => ({
                name: item.name,
                url: item.path,
                id: item.id,
                status: 'success', // 标记为已上传
                size: item.size
              }))
            }
            if (applyDetail.projectEnvVO.projectEnvDeviceVOList) {
              this.formData.devices = applyDetail.projectEnvVO.projectEnvDeviceVOList
            }
          }

          if (applyDetail.projectEnvVO && applyDetail.projectEnvVO.resourceType !== undefined) {
            if (applyDetail.projectEnvVO.resourceType === 0) {
              this.resourceType = 'virtual'
            } else if (applyDetail.projectEnvVO.resourceType === 1) {
              this.resourceType = 'network'
            }
          }

          // 设置场景信息
          if (applyDetail.projectEnvVO && applyDetail.projectEnvVO.projectEnvSceneVO) {
            this.formData.scenarioId = applyDetail.projectEnvVO.projectEnvSceneVO.sceneId || ''
            this.formData.scenarioName = applyDetail.projectEnvVO.projectEnvSceneVO.sceneName || ''
            this.formData.scenarioDescription = applyDetail.projectEnvVO.projectEnvSceneVO.description || ''
          }
          const taskRes = await processTaskAPI(this.applyId)
          // 处理任务数据
          const tasks = taskRes.data.data || []
          this.processTasks = this.getDisplayTasks(tasks)
          this.activeAttachmentType = this.processTasks.length > 0 ? this.processTasks[0].id : ''

          const addProcessName = (arr) => {
            arr.forEach(item => {
              item.processName = item.name
              if (Array.isArray(item.children) && item.children.length > 0) {
                addProcessName(item.children)
              }
            })
            return arr
          }

          // const testingTasksData = addProcessName(tasks)
          // this.testingTasksData = testingTasksData
          // 如果是厂商状态为1，则过滤任务列表，只保留用户选择的测试任务
          if (this.projectDetail.vendorStatus === 1) {
            // 设置树形控件的选中状态
            this.$nextTick(() => {
              if (this.$refs.testingTaskTree) {
                this.processTasks.forEach(taskId => {
                  this.$refs.testingTaskTree.setChecked(taskId.id, true, false)
                })
              }
            })
          }

          // 设置当前附件类型
          if (this.processTasks.length > 0) {
            this.activeAttachmentType = this.processTasks[0].id
          }

          // 获取附件信息
          if (applyDetail.attachments && Array.isArray(applyDetail.attachments)) {
            this.attachmentList = applyDetail.attachments
            this.getList(true, this.activeAttachmentType)
            // 初始化附件表格的数据
            this.$nextTick(() => {
              if (this.$refs.attachmentTableRef) {
                this.$refs.attachmentTableRef.initializeLocalData()
              }
            })
          }
        }
      }
    },

    // 加载正常模式的项目详情
    loadNormalProjectDetail(id) {
      testingItemsDetailAPI(id)
        .then(async res => {
          if (res.data.code === 0) {
            // 存储项目详情数据
            const projectData = res.data.data
            // 更新项目基本信息
            this.projectDetail = {
              id: projectData.id,
              name: projectData.name,
              round: projectData.round,
              status: projectData.status,
              managerId: projectData.managerId,
              managerName: projectData.managerName,
              processId: projectData.processId,
              processName: projectData.processName,
              planBeginDate: projectData.planBeginDate,
              actualBeginTime: projectData.actualBeginTime,
              actualEndTime: projectData.actualEndTime,
              createBy: projectData.createBy,
              createByName: projectData.createByName,
              createAt: projectData.createAt,
              description: projectData.description,
              vendorStatus: projectData.vendorStatus,
              productName: this.$route.query.productName,
              vendorName: this.$route.query.vendorName,
              productVersion: this.$route.query.productVersion
            }
            const taskRes = await processTaskQueryAPI(projectData.processId)
            // 处理任务数据
            const tasks = taskRes.data.data || []
            // 如果是厂商状态为1，则过滤任务列表，只保留用户选择的测试任务
            if (projectData.vendorStatus === 1) {
              this.testingTasksData = tasks
              // 初始化processTasks为空数组，稍后会根据选择的任务填充
              this.processTasks = []
            } else {
              // 不是厂商状态1，直接使用所有任务
              this.processTasks = this.getDisplayTasks(tasks)
              this.activeAttachmentType = this.processTasks.length > 0 ? this.processTasks[0].id : ''
              this.testingTasksData = tasks
            }
            this.getProjectDetail(id)
          }
          this.loading = false
        })
        .catch(error => {
          console.error('获取项目详情失败：', error)
          this.loading = false
        })
    },
    getDisplayTasks(tasks) {
      const result = []
      tasks.forEach(task => {
        if (Array.isArray(task.children) && task.children.length > 0) {
          task.children.forEach(child => {
            result.push({ id: child.sourceTaskId || child.id, processName: child.name || child.processName })
          })
        } else {
          result.push({ id: task.sourceTaskId || task.id, processName: task.name || task.processName })
        }
      })
      return result
    },
    handleSubmit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          // 检查环境配置
          if (!this.validateEnvironment()) {
            if (this.deployType === 'local') {
              if (this.resourceType === 'virtual') {
                this.$message.error('请至少添加一个虚拟机配置')
              } else {
                this.$message.error('请完善仿真场景信息')
              }
            } else {
              this.$message.error('请至少添加一个设备信息')
            }
            return
          }

          // 检查检测范围
          if (
            !this.validateTestingTasks() &&
            this.projectDetail.vendorStatus === 1
          ) {
            this.$message.error('请至少选择一个检测任务')
            return
          }
          const id = this.formData.id
          // 构建请求参数
          const requestParams = {
            envType: this.deployType === 'local' ? 0 : 1, // 部署类型：0-本平台部署，1-外部部署
            resourceType:
              this.resourceType === 'virtual'
                ? 0
                : this.resourceType === 'network'
                  ? 1
                  : 2, // 资源类型：0-虚拟机，1-场景，2-设备
            vmList: [], // 改为虚拟机对象列表
            deviceList: [], // 改为设备对象列表
            envSceneBO: {
              id: '',
              description: ''
            },
            deviceAttachmentList: [], // 改为设备附件对象列表
            attachmentList: [], // 改为附件对象列表
            taskIdList: this.taskIdListPostData || [] // 添加检测任务ID列表
          }

          // 如果是编辑模式，添加申请ID
          if (this.isEditMode && this.applyId) {
            requestParams.applyId = this.applyId
          }

          if (this.deployType === 'external') {
            requestParams.resourceType = 2
          }
          // 根据部署类型处理不同参数
          if (this.deployType === 'local') {
            if (this.resourceType === 'virtual') {
              // 处理虚拟机资源 - 传递完整的虚拟机对象
              requestParams.vmList = this.formData.virtualMachines.map(vm => ({
                id: vm.id || null,
                name: vm.deviceName || vm.name,
                imageId: vm.imageId,
                imageName: vm.imageName,
                cpuCount: vm.cpu || vm.cpuCount,
                memSize: vm.memory || vm.memSize,
                memUnit: vm.memoryUnit || vm.memUnit || 'GB',
                systemDiskSize: vm.system || vm.systemDiskSize,
                dataDiskSize: vm.data || vm.dataDiskSize,
                workstation: vm.workstation
              }))
            } else if (this.resourceType === 'network') {
              // 处理网络编排场景
              requestParams.envSceneBO = {
                id: this.formData.scenarioId || 0,
                name: this.formData.scenarioName,
                description: this.formData.scenarioDescription || ''
              }
            }
          } else if (this.deployType === 'external') {
            // 处理外部设备对象列表 - 传递完整的设备对象
            requestParams.deviceList = this.formData.devices.map(device => ({
              name: device.name,
              target: device.target,
              targetPort: device.targetPort,
              type: device.type,
              remark: device.remark,
              deviceAccountBOList: device.deviceAccountBOList || []
            }))

            // 处理外部设备附件列表 - 传递完整的附件对象
            if (this.deviceAttachmentList && this.deviceAttachmentList.length > 0) {
              this.deviceAttachmentList.forEach(attachment => {
                // 检查是否已经添加过该附件
                const exists = requestParams.deviceAttachmentList.some(
                  item => item.id === attachment.id
                )
                if (!exists) {
                  requestParams.deviceAttachmentList.push({
                    id: attachment.id,
                    fileId: attachment.id,
                    fileUrl: attachment.path,
                    fileName: attachment.name,
                    fileSize: attachment.size
                  })
                }
              })
            }
          }
          // 处理attachmentList中的附件
          if (this.attachmentList && this.attachmentList.length > 0) {
            // 先确保所有附件都有必要的字段
            const processedAttachments = this.attachmentList.map(attachment => {
              return {
                ...attachment,
                taskId: attachment.taskId || attachment.typeId,
                projectId: attachment.projectId || this.projectId
              }
            })

            processedAttachments.forEach(attachment => {
              // 检查是否已经添加过该附件
              const exists = requestParams.attachmentList.some(
                item => item.fileUrl === attachment.fileUrl
              )
              if (!exists) {
                requestParams.attachmentList.push({
                  id: attachment.fileId,
                  fileId: attachment.fileId,
                  fileUrl: attachment.fileUrl,
                  fileName: attachment.fileName,
                  fileSize: attachment.fileSize,
                  taskId: attachment.taskId,
                  projectId: attachment.projectId || this.projectId,
                  typeName: attachment.typeName
                })
              }
            })
            // 剔除taskId不在taskIdList里的数据
            const taskIdSet = new Set(requestParams.taskIdList)
            requestParams.attachmentList = requestParams.attachmentList.filter(item => {
              return taskIdSet.has(Number(item.taskId))
            })
          }
          this.loading = true
          projectTestApplyAPI(id, requestParams).then(res => {
            if (res.data && res.data.code === 0) {
              this.$message.success(this.isEditMode ? '重新送审成功' : '送审成功')
              this.loading = false
              this.$router.go(-1)
            } else {
              this.$message.error(res.data.msg || '送审失败')
              this.loading = false
            }
          }).catch(error => {
            console.error('送审失败：', error)
            this.$message.error('送审失败，请稍后重试')
            this.loading = false
          })
        }
      })
    },
    removeDevice(index) {
      this.$confirm('确定要删除该设备信息吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 直接从本地数据中删除
        this.$refs.deviceTableRef.removeDeviceLocal(index)
        this.$message.success('删除成功')
      })
    },
    // 设备弹窗
    showAddDevice(type, data) {
      this.modalWidth = '600px'
      this.modalName = type == 'add' ? 'addDevice' : 'editDevice'
      this.deviceBtnType = type
      this.$set(this, 'editDeviceData', data)
    },
    confirmCall: function(type, data) {
      if (type === 'close') {
        this.modalClose()
      } else if (type === 'refresh') {
        this.$refs.deviceTableRef.getList(true)
      } else if (type === 'confirm_device') {
        if (this.deviceBtnType === 'add') {
          // 添加设备配置到本地数据
          this.$refs.deviceTableRef.addDevice(data)
        } else if (this.deviceBtnType === 'edit') {
          // 编辑设备：如果有id则根据id找，否则使用临时索引
          if (data.id) {
            const index = this.formData.devices.findIndex(device => device.id === data.id)
            if (index !== -1) {
              this.$refs.deviceTableRef.updateDevice(index, data)
            }
          } else if (data._index !== undefined) {
            // 使用临时索引更新
            this.$refs.deviceTableRef.updateDevice(data._index, data)
          }
        }
        this.modalClose()
      }
    },
    showAddVirtualMachineDrawer(type, data) {
      this.drawerName = type == 'add' ? 'addVirtualMachine' : 'editVirtualMachine'
      this.virtualBtnType = type
      this.$set(this, 'editVirtualData', data)
    },
    showSelectScenarioDrawer() {
      this.drawerName = 'selectScenario'
    },
    drawerConfirmCall(type, data) {
      if (type === 'confirm_virtual_machine') {
        if (this.virtualBtnType === 'add') {
          // 添加虚拟机配置到本地数据
          this.$refs.virtualTableRef.addVirtualMachine(data)
        } else if (this.virtualBtnType === 'edit') {
          // 编辑虚拟机：如果有id则根据id找，否则使用临时索引
          if (data.id) {
            const index = this.formData.virtualMachines.findIndex(vm => vm.id === data.id)
            if (index !== -1) {
              this.$refs.virtualTableRef.updateVirtualMachine(index, data)
            }
          } else if (data._index !== undefined) {
            // 使用临时索引更新
            this.$refs.virtualTableRef.updateVirtualMachine(data._index, data)
          }
        }
        this.drawerClose()
      } else if (type === 'select_scenario') {
        // 处理选择的仿真场景数据
        this.formData.scenarioId = data.id
        this.formData.scenarioName = data.sceneName
        this.drawerClose()
      }
    },
    removeVirtualMachine(index) {
      this.$confirm('确定要删除该虚拟机配置吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          // 直接从本地数据中删除
          this.$refs.virtualTableRef.removeVirtualMachineLocal(index)
          this.$message.success('删除成功')
        })
    },
    beforeUpload(file) {
      const isLt600M = file.size / 1024 / 1024 < 600
      if (!isLt600M) {
        this.$message.error('文件大小不能超过600MB')
        return false
      }
      return true
    },
    previewFile(file) {
      let fileType = file.fileType
      if (fileType) {
        if (
          file &&
          !this.fileCouldPreview.includes(fileType.toLowerCase())
        ) {
          this.$message.warning('该文件类型无法预览')
          return
        }
      }
      if (!fileType) {
        fileType = file.fileName.split('.')[file.fileName.split('.').length - 1]
        if (
          file &&
          !this.fileCouldPreview.includes(fileType.toLowerCase())
        ) {
          this.$message.warning('该文件类型无法预览')
          return
        }
      }
      // 判断是否为xlsx类型
      const isXlsx = fileType && fileType.toLowerCase() === 'xlsx'
      if (file.path) {
        const fileUrl = window.ADMIN_CONFIG.VIP_URL + file.path
        if (isXlsx) {
          this.previewUrl = this.viewFileUrl + encodeURIComponent(btoa(fileUrl)) + '&officePreviewType=html'
        } else {
          this.previewUrl = this.viewFileUrl + encodeURIComponent(btoa(fileUrl))
        }
        window.open(this.previewUrl)
        return
      }
      if (file.fileUrl) {
        const fileUrl = window.ADMIN_CONFIG.VIP_URL + file.fileUrl
        if (isXlsx) {
          this.previewUrl = this.viewFileUrl + encodeURIComponent(btoa(fileUrl)) + '&officePreviewType=html'
        } else {
          this.previewUrl = this.viewFileUrl + encodeURIComponent(btoa(fileUrl))
        }
        window.open(this.previewUrl)
        return
      }
    },
    updateFile(file) {
      // 设置当前更新的文件信息
      this.uploadForm.currentUpdateFile = {
        id: file.id,
        fileName: file.fileName
      }
      this.uploadForm.task = file.typeName || this.activeAttachmentTypeName
      this.uploadForm.fileList = []
      this.uploadDialogVisible = true
    },
    removeFile(index) {
      this.$confirm('确定要删除该附件吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 直接从本地数据中删除
        this.$refs.attachmentTableRef.removeAttachmentLocal(index)
        this.$message.success('删除成功')
      })
    },
    formatFileSize(size) {
      if (size < 1024) {
        return size + ' B'
      } else if (size < 1024 * 1024) {
        return (size / 1024).toFixed(2) + ' KB'
      } else {
        return (size / 1024 / 1024).toFixed(2) + ' MB'
      }
    },
    validateEnvironment() {
      if (this.deployType === 'local') {
        if (this.resourceType === 'virtual') {
          return this.formData.virtualMachines.length > 0
        } else {
          return (
            this.formData.scenarioId &&
            this.formData.scenarioName &&
            this.formData.scenarioDescription
          )
        }
      } else {
        return this.formData.devices.length > 0
      }
    },
    addFile() {
      // 查找当前附件类型对应的任务
      const currentTask = this.processTasks.find(
        task => task.id === this.activeAttachmentType
      );
      (this.uploadForm.task = currentTask
        ? currentTask.processName
        : this.activeAttachmentTypeName);
      (this.uploadForm.fileList = [])
      this.uploadForm.currentUpdateFile.id = ''
      this.uploadDialogVisible = true
    },
    handleDeviceFileChange(file, fileList) {
      const maxSize = this.maxFileSize * 1024 * 1024
      if (file.size > maxSize) {
        this.$message.error(`上传文件不能超过${this.maxFileSize}MB!`)
        fileList.pop()
      }
      this.deviceAttachmentList = fileList
    },
    handleFileChange(file, fileList) {
      this.uploadForm.fileList = fileList
    },
    handleFileRemove(file, fileList) {
      this.uploadForm.fileList = fileList
    },
    submitUpload() {
      if (!this.uploadForm.fileList || this.uploadForm.fileList.length === 0) {
        this.$message.error('请先上传文件')
        return
      }

      this.uploadLoading = true

      // 记录成功和失败的数量
      let successCount = 0
      let failCount = 0
      const totalFiles = this.uploadForm.fileList.length

      // 为每个文件处理本地数据
      this.uploadForm.fileList.forEach(file => {
        // 检查文件是否已上传成功并有响应数据
        if (!file.response || file.response.code !== 0) {
          failCount++
          console.error('文件上传失败:', file.name)
          // 当所有文件处理完成时
          if (successCount + failCount === totalFiles) {
            this.handleUploadComplete(successCount, failCount)
          }
          return
        }

        const fileData = file.response.data
        const attachmentData = {
          id: null, // 将在组件中生成临时ID
          fileId: fileData.id,
          fileUrl: fileData.path,
          fileName: fileData.name,
          fileSize: file.size,
          taskId: this.activeAttachmentType,
          projectId: this.projectId,
          typeName: this.uploadForm.task
        }

        // 添加到本地数据
        this.$refs.attachmentTableRef.addAttachment(attachmentData)
        successCount++

        // 当所有文件处理完成时
        if (successCount + failCount === totalFiles) {
          this.handleUploadComplete(successCount, failCount)
        }
      })
    },

    // 处理上传完成后的操作
    handleUploadComplete(successCount, failCount) {
      this.uploadLoading = false
      if (successCount > 0) {
        this.$message.success(`成功添加${successCount}个文件${failCount > 0 ? '，' + failCount + '个文件添加失败' : ''}`)
        this.uploadDialogVisible = false
      } else {
        this.$message.error('所有文件添加失败')
      }
    },
    submitUpdateFile() {
      if (!this.uploadForm.fileList || this.uploadForm.fileList.length === 0) {
        this.$message.error('请先上传文件')
        return
      }

      // 更新操作只处理第一个文件
      if (this.uploadForm.fileList.length > 1) {
        this.$message.warning('更新操作只会使用第一个文件，其他文件将被忽略')
      }

      const fileId = this.uploadForm.currentUpdateFile.id
      const file = this.uploadForm.fileList[0]

      // 检查文件是否已上传成功并有响应数据
      if (!file.response || file.response.code !== 0) {
        this.$message.error('文件上传失败，请重新上传')
        return
      }

      const fileData = file.response.data
      const updatedData = {
        id: fileId,
        fileId: fileData.id,
        fileUrl: fileData.path,
        fileName: fileData.name,
        fileSize: file.size,
        taskId: this.activeAttachmentType,
        projectId: this.projectId,
        typeName: this.uploadForm.task
      }

      this.uploadLoading = true
      // 更新本地数据
      this.$refs.attachmentTableRef.updateAttachment(fileId, updatedData)
      this.uploadLoading = false
      this.uploadDialogVisible = false
      this.$message.success('文件更新成功')
    },
    // 获取全部附件
    getAllAttachments() {
      // 改为本地数据同步，不再调用API
      if (this.$refs.attachmentTableRef) {
        this.$refs.attachmentTableRef.syncToParent()
      }
    },
    // 处理上传成功
    handleUploadSuccess(res, file, fileList) {
      if (res.code === 0) {
        // 上传成功处理
        file.id = res.data.id
        file.path = res.data.path
        this.$message.success('文件上传成功')
      } else {
        this.$message.error(res.msg || '上传失败')
        // 从文件列表中移除失败的文件
        this.uploadForm.fileList = []
      }
      this.uploadLoading = false
    },
    // 处理上传失败
    handleUploadError(err, file, fileList) {
      console.error('上传失败', err)
      this.$message.error('文件上传失败，请稍后重试')
      this.uploadForm.fileList = []
      this.uploadLoading = false
    },
    /** 处理文件上传成功 */
    handleDeviceUploadSuccess(res, file, fileList) {
      if (res.code === 0) {
        file.id = res.data.id
        file.path = res.data.path
        this.$message.success('上传成功！')
        this.$refs.form.validateField('deviceFile')
      } else {
        this.$message.error(res.msg)
      }
    },
    /** 处理文件移除 */
    handleDeviceFileRemove(file, fileList) {
      this.deviceAttachmentList = fileList
    },
    // 处理检测任务选择变化
    handleTestingTaskCheckChange(data, checked) {
      const taskId = data.id

      // 维护formData.testingTasks
      if (checked) {
        if (!this.formData.testingTasks.includes(taskId)) {
          this.formData.testingTasks.push(taskId)
        }
      } else {
        const index = this.formData.testingTasks.indexOf(taskId)
        if (index !== -1) {
          this.formData.testingTasks.splice(index, 1)
        }
      }
      if (this.$refs.testingTaskTree.getHalfCheckedKeys().length > 0 || this.formData.testingTasks.length > 0) {
        // 子节点被选中，加入其父节点
        this.taskIdListPostData = [...new Set([...this.formData.testingTasks, ...this.$refs.testingTaskTree.getHalfCheckedKeys()])]
      }

      // 只保留已勾选的任务（优化：只展示被选中的叶子节点，父节点选中时展示其所有子节点）
      if (this.projectDetail.vendorStatus === 1) {
        // 获取所有被选中的叶子节点
        const checkedKeys = this.formData.testingTasks
        this.processTasks = this.getCheckedLeafTasks(this.testingTasksData, checkedKeys)
        const taskIds = this.processTasks.map(item => item.id)
        // 选中的任务id
        this.checkedTaskIds = taskIds
        this.getAllAttachments(taskIds)
        // 如果当前activeAttachmentType被移除了，切换到第一个
        if (!this.processTasks.some(t => t.id === this.activeAttachmentType)) {
          this.activeAttachmentType = this.processTasks.length > 0 ? this.processTasks[0].id : ''
          this.getList(true, this.activeAttachmentType)
        }
      }
    },
    // 递归获取所有被选中的叶子节点
    getCheckedLeafTasks(treeData, checkedKeys) {
      let result = []
      const loop = (nodes) => {
        nodes.forEach(node => {
          if (node.children && node.children.length > 0) {
            // 如果父节点被选中，加入其所有子节点（只加叶子节点）
            if (checkedKeys.includes(node.id)) {
              // 递归收集所有子节点的叶子节点
              result = result.concat(this.getCheckedLeafTasks(node.children, node.children.map(child => child.id)))
            } else {
              // 递归处理子节点
              loop(node.children)
            }
          } else {
            // 叶子节点被选中
            if (checkedKeys.includes(node.id)) {
              result.push(node)
            }
          }
        })
      }
      loop(treeData)
      return result
    },
    // 验证检测范围
    validateTestingTasks() {
      return (
        this.formData.testingTasks && this.formData.testingTasks.length > 0
      )
    }
  }
}
</script>

<style scoped lang="scss">
::v-deep.upload-dialog .el-dialog__footer{
  padding: 0;
}
.dialog-footer {
  text-align: right;
  padding: 14px 24px;
  background: transparent;
  border-top: 1px solid #f0f0f0;
  border-radius: 0 0 2px 2px;
  .el-button--text {
    padding: 4px 15px;
  }
}
.info-card,
.form-card {
  margin-bottom: 10px;

  .card-header {
    font-weight: bold;
  }
}

.info-row {
  display: flex;
  margin-bottom: 15px;

  .info-item {
    flex: 1;
    display: flex;

    .label {
      color: var(--neutral-700);
      font-weight: 500;
      width: 100px;
      text-align: right;
      padding-right: 30px;
    }

    .value {
      flex: 1;
    }
  }
}

.env-tabs {
  display: flex;
  align-items: center;
  margin-bottom: 20px;

  .label-block {
    padding-right: 12px;
    width: 100px;
    text-align: left;
    color: var(--neutral-700);
    font-weight: 500;
    flex-shrink: 0;
  }

  .tab-buttons {
    display: flex;

    .el-button {
      padding: 8px 20px;
      border-radius: 0;
    }

    .el-button + .el-button {
      margin-left: 0;
    }

    .el-button:first-child {
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
    }

    .el-button:last-child {
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
    }
  }
}

.deploy-content,
.resource-content {
  margin-top: 20px;
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;

  span {
    width: 100px;
    text-align: left;
    color: var(--neutral-700);
    font-weight: 500;
  }
}

.empty-data {
  padding: 20px;
  text-align: center;
  color: #909399;
  background: #f5f7fa;
  border-radius: 4px;
}

.attachment-section {
  margin-bottom: 20px;

  .switch-group {
    margin-bottom: 15px;
  }

  .file-list-area {
    .add-file-btn {
      margin-bottom: 15px;
    }

    .empty-file-list {
      padding: 30px 0;
      text-align: center;
      background-color: #f8f8f8;
      border-radius: 4px;
      color: #909399;

      .empty-text {
        font-size: 14px;
      }
    }
  }
}

.el-input {
  width: 66.6%;
}

.el-tabs--card > .el-tabs__header .el-tabs__item.is-active {
  border-bottom-color: var(--color-600);
}

.required-item {
  ::v-deep .el-form-item__label:before {
    content: "*";
    color: #f56c6c;
    margin-right: 4px;
  }
}

.align-items {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.el-tree {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #e6e6e6;
  padding: 10px;
  border-radius: 4px;
}
::v-deep .device-upload .el-form-item__content {
  line-height: 30px;
  .el-upload-list {
    margin-top: -8px;
  }
}
</style>
